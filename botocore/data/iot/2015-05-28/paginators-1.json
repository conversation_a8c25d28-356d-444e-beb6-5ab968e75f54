{"pagination": {"ListCACertificates": {"input_token": "marker", "output_token": "nextM<PERSON><PERSON>", "limit_key": "pageSize", "result_key": "certificates"}, "ListCertificates": {"input_token": "marker", "output_token": "nextM<PERSON><PERSON>", "limit_key": "pageSize", "result_key": "certificates"}, "ListCertificatesByCA": {"input_token": "marker", "output_token": "nextM<PERSON><PERSON>", "limit_key": "pageSize", "result_key": "certificates"}, "ListOutgoingCertificates": {"input_token": "marker", "output_token": "nextM<PERSON><PERSON>", "limit_key": "pageSize", "result_key": "outgoingCertificates"}, "ListPolicies": {"input_token": "marker", "output_token": "nextM<PERSON><PERSON>", "limit_key": "pageSize", "result_key": "policies"}, "ListPolicyPrincipals": {"input_token": "marker", "output_token": "nextM<PERSON><PERSON>", "limit_key": "pageSize", "result_key": "principals"}, "ListPrincipalPolicies": {"input_token": "marker", "output_token": "nextM<PERSON><PERSON>", "limit_key": "pageSize", "result_key": "policies"}, "ListPrincipalThings": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "things"}, "ListThingTypes": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "thingTypes"}, "ListThings": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "things"}, "ListTopicRules": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "rules"}, "ListActiveViolations": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "activeViolations"}, "ListAttachedPolicies": {"input_token": "marker", "limit_key": "pageSize", "output_token": "nextM<PERSON><PERSON>", "result_key": "policies"}, "ListAuditFindings": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "findings"}, "ListAuditTasks": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "tasks"}, "ListAuthorizers": {"input_token": "marker", "limit_key": "pageSize", "output_token": "nextM<PERSON><PERSON>", "result_key": "authorizers"}, "ListBillingGroups": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "billingGroups"}, "ListIndices": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "indexNames"}, "ListJobExecutionsForJob": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "executionSummaries"}, "ListJobExecutionsForThing": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "executionSummaries"}, "ListJobs": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "jobs"}, "ListOTAUpdates": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "otaUpdates"}, "ListRoleAliases": {"input_token": "marker", "limit_key": "pageSize", "output_token": "nextM<PERSON><PERSON>", "result_key": "roleAliases"}, "ListScheduledAudits": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "scheduledAudits"}, "ListSecurityProfiles": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "securityProfileIdentifiers"}, "ListSecurityProfilesForTarget": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "securityProfileTargetMappings"}, "ListStreams": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "streams"}, "ListTagsForResource": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "tags"}, "ListTargetsForPolicy": {"input_token": "marker", "limit_key": "pageSize", "output_token": "nextM<PERSON><PERSON>", "result_key": "targets"}, "ListTargetsForSecurityProfile": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "securityProfileTargets"}, "ListThingGroups": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "thingGroups"}, "ListThingGroupsForThing": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "thingGroups"}, "ListThingRegistrationTasks": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "taskIds"}, "ListThingsInBillingGroup": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "things"}, "ListThingsInThingGroup": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "things"}, "ListV2LoggingLevels": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "logTargetConfigurations"}, "ListViolationEvents": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "violationEvents"}, "ListAuditMitigationActionsExecutions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "actionsExecutions"}, "ListAuditMitigationActionsTasks": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "tasks"}, "ListAuditSuppressions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "suppressions"}, "ListDimensions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "dimensionNames"}, "ListDomainConfigurations": {"input_token": "marker", "limit_key": "pageSize", "output_token": "nextM<PERSON><PERSON>", "result_key": "domainConfigurations"}, "ListMitigationActions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "actionIdentifiers"}, "ListProvisioningTemplateVersions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "versions"}, "ListProvisioningTemplates": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "templates"}, "ListThingRegistrationTaskReports": {"input_token": "nextToken", "limit_key": "maxResults", "non_aggregate_keys": ["reportType"], "output_token": "nextToken", "result_key": "resourceLinks"}, "ListTopicRuleDestinations": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "destinationSummaries"}, "ListThingPrincipals": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "principals"}, "GetBehaviorModelTrainingSummaries": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "summaries"}, "ListCustomMetrics": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "metricNames"}, "ListDetectMitigationActionsExecutions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "actionsExecutions"}, "ListDetectMitigationActionsTasks": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "tasks"}, "ListFleetMetrics": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "fleetMetrics"}, "ListJobTemplates": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "jobTemplates"}, "ListMetricValues": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "metricDatumList"}, "ListManagedJobTemplates": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "managedJobTemplates"}, "ListRelatedResourcesForAuditFinding": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "relatedResources"}, "ListPackageVersions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "packageVersionSummaries"}, "ListPackages": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "packageSummaries"}, "ListSbomValidationResults": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "validationResultSummaries"}, "ListPrincipalThingsV2": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "principalThingObjects"}, "ListThingPrincipalsV2": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "thingPrincipalObjects"}, "ListCommandExecutions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "commandExecutions"}, "ListCommands": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "commands"}}}